[2025-06-20 11:02:47] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["53"],"content-type":["application/json"]},"body":{"email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T11:02:47.847301Z"} 
[2025-06-20 11:02:47] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":200,"duration_ms":28.7,"response_size":274,"timestamp":"2025-06-20T11:02:47.875094Z"} 
[2025-06-20 11:02:47] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["55"],"content-type":["application/json"]},"body":{"email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T11:02:47.886169Z"} 
[2025-06-20 11:02:47] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":3.08,"response_size":199,"timestamp":"2025-06-20T11:02:47.889180Z"} 
[2025-06-20 11:02:47] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"authorization":["***FILTERED***"]},"body":[],"timestamp":"2025-06-20T11:02:47.896972Z"} 
[2025-06-20 11:02:47] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":200,"duration_ms":8.45,"response_size":374,"timestamp":"2025-06-20T11:02:47.905353Z"} 
[2025-06-20 11:02:47] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:47.912380Z"} 
[2025-06-20 11:02:47] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":401,"duration_ms":0.61,"response_size":145,"timestamp":"2025-06-20T11:02:47.912938Z"} 
[2025-06-20 11:02:47] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["53"],"content-type":["application/json"]},"body":{"email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T11:02:47.919605Z"} 
[2025-06-20 11:02:47] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":200,"duration_ms":1.95,"response_size":274,"timestamp":"2025-06-20T11:02:47.921505Z"} 
[2025-06-20 11:02:47] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"authorization":["***FILTERED***"]},"body":[],"timestamp":"2025-06-20T11:02:47.922109Z"} 
[2025-06-20 11:02:47] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":200,"duration_ms":1.72,"response_size":374,"timestamp":"2025-06-20T11:02:47.923824Z"} 
[2025-06-20 11:02:47] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/logout","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"authorization":["***FILTERED***"]},"body":[],"timestamp":"2025-06-20T11:02:47.924433Z"} 
[2025-06-20 11:02:47] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/logout","status_code":200,"duration_ms":0.33,"response_size":117,"timestamp":"2025-06-20T11:02:47.924761Z"} 
[2025-06-20 11:02:47] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"authorization":["***FILTERED***"]},"body":[],"timestamp":"2025-06-20T11:02:47.931802Z"} 
[2025-06-20 11:02:47] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":200,"duration_ms":1.89,"response_size":374,"timestamp":"2025-06-20T11:02:47.933619Z"} 
[2025-06-20 11:02:47] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/revoke-all-tokens","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"authorization":["***FILTERED***"]},"body":[],"timestamp":"2025-06-20T11:02:47.934266Z"} 
[2025-06-20 11:02:47] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/revoke-all-tokens","status_code":200,"duration_ms":0.36,"response_size":123,"timestamp":"2025-06-20T11:02:47.934620Z"} 
[2025-06-20 11:02:47] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["42"],"content-type":["application/json"]},"body":{"email":"invalid-email","password":"***FILTERED***"},"timestamp":"2025-06-20T11:02:47.941603Z"} 
[2025-06-20 11:02:47] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":3.18,"response_size":276,"timestamp":"2025-06-20T11:02:47.944727Z"} 
[2025-06-20 11:02:47] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:47.962385Z"} 
[2025-06-20 11:02:47] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":401,"duration_ms":0.63,"response_size":145,"timestamp":"2025-06-20T11:02:47.962934Z"} 
[2025-06-20 11:02:47] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:47.975963Z"} 
[2025-06-20 11:02:47] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":403,"duration_ms":1.5,"response_size":142,"timestamp":"2025-06-20T11:02:47.977395Z"} 
[2025-06-20 11:02:47] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["39"],"content-type":["application/json"]},"body":{"email":"invalid-email","password":null},"timestamp":"2025-06-20T11:02:47.990246Z"} 
[2025-06-20 11:02:47] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":0.96,"response_size":257,"timestamp":"2025-06-20T11:02:47.991139Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.003053Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":401,"duration_ms":0.42,"response_size":145,"timestamp":"2025-06-20T11:02:48.003412Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.017160Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations","status_code":401,"duration_ms":0.65,"response_size":145,"timestamp":"2025-06-20T11:02:48.017723Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.028819Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations","status_code":200,"duration_ms":9.9,"response_size":1606,"timestamp":"2025-06-20T11:02:48.038652Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.049620Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations","status_code":200,"duration_ms":1.87,"response_size":672,"timestamp":"2025-06-20T11:02:48.051423Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.060928Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations","status_code":403,"duration_ms":1.57,"response_size":134,"timestamp":"2025-06-20T11:02:48.062439Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?role=member","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"role":"member"},"timestamp":"2025-06-20T11:02:48.072729Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?role=member","status_code":200,"duration_ms":1.97,"response_size":1148,"timestamp":"2025-06-20T11:02:48.074638Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?model_id=1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"model_id":"1"},"timestamp":"2025-06-20T11:02:48.075196Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?model_id=1","status_code":200,"duration_ms":1.64,"response_size":1146,"timestamp":"2025-06-20T11:02:48.076839Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?model_type=App%5CModels%5COrganisation","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"model_type":"App\\Models\\Organisation"},"timestamp":"2025-06-20T11:02:48.077359Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?model_type=App%5CModels%5COrganisation","status_code":200,"duration_ms":1.85,"response_size":1619,"timestamp":"2025-06-20T11:02:48.079209Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?status=valid","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"status":"valid"},"timestamp":"2025-06-20T11:02:48.089929Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?status=valid","status_code":200,"duration_ms":1.75,"response_size":670,"timestamp":"2025-06-20T11:02:48.091620Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?status=expired","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"status":"expired"},"timestamp":"2025-06-20T11:02:48.092176Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?status=expired","status_code":200,"duration_ms":1.45,"response_size":670,"timestamp":"2025-06-20T11:02:48.093620Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?status=used_up","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"status":"used_up"},"timestamp":"2025-06-20T11:02:48.094182Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?status=used_up","status_code":200,"duration_ms":1.5,"response_size":670,"timestamp":"2025-06-20T11:02:48.095682Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?per_page=10","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"per_page":"10"},"timestamp":"2025-06-20T11:02:48.111082Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?per_page=10","status_code":200,"duration_ms":3.05,"response_size":4855,"timestamp":"2025-06-20T11:02:48.114089Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?page=2&per_page=10","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"per_page":"10","page":"2"},"timestamp":"2025-06-20T11:02:48.114723Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?page=2&per_page=10","status_code":200,"duration_ms":2.75,"response_size":4856,"timestamp":"2025-06-20T11:02:48.117472Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978d01-643e-70fb-849e-942a89438178","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.127616Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978d01-643e-70fb-849e-942a89438178","status_code":200,"duration_ms":1.16,"response_size":589,"timestamp":"2025-06-20T11:02:48.128715Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978d01-6449-7102-ae21-c289afea2838","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.138459Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978d01-6449-7102-ae21-c289afea2838","status_code":200,"duration_ms":1.11,"response_size":595,"timestamp":"2025-06-20T11:02:48.139505Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978d01-6455-71ed-9d08-fdae279a150c","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.150096Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978d01-6455-71ed-9d08-fdae279a150c","status_code":410,"duration_ms":0.76,"response_size":147,"timestamp":"2025-06-20T11:02:48.150738Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978d01-6460-7105-9882-6d2a081e2cc6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.160787Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978d01-6460-7105-9882-6d2a081e2cc6","status_code":410,"duration_ms":0.63,"response_size":171,"timestamp":"2025-06-20T11:02:48.161356Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978d01-646a-7183-a634-32b2956d73ae","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.171156Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978d01-646a-7183-a634-32b2956d73ae","status_code":200,"duration_ms":1.11,"response_size":618,"timestamp":"2025-06-20T11:02:48.172206Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978d01-6475-73a8-8706-0f7e3fa40465","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.181901Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978d01-6475-73a8-8706-0f7e3fa40465","status_code":200,"duration_ms":1.17,"response_size":589,"timestamp":"2025-06-20T11:02:48.183016Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/invitations/01978d01-6481-71df-b9b0-39778992617e/accept","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.193828Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/invitations/01978d01-6481-71df-b9b0-39778992617e/accept","status_code":422,"duration_ms":1.71,"response_size":223,"timestamp":"2025-06-20T11:02:48.195465Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/invitations/01978d01-648d-7086-82e1-257c97f195d0/accept","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.206396Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/invitations/01978d01-648d-7086-82e1-257c97f195d0/accept","status_code":401,"duration_ms":0.6,"response_size":145,"timestamp":"2025-06-20T11:02:48.206927Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/invitations/01978d01-6498-71c9-bef4-8a0282310e55/accept","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.217337Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/invitations/01978d01-6498-71c9-bef4-8a0282310e55/accept","status_code":200,"duration_ms":3.17,"response_size":462,"timestamp":"2025-06-20T11:02:48.220438Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/invitations/01978d01-64a7-7199-883b-41bfe9b518d5/accept","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.231868Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/invitations/01978d01-64a7-7199-883b-41bfe9b518d5/accept","status_code":422,"duration_ms":0.77,"response_size":246,"timestamp":"2025-06-20T11:02:48.232528Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.252597Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations","status_code":200,"duration_ms":2.75,"response_size":1858,"timestamp":"2025-06-20T11:02:48.255255Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations?status=active","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":{"status":"active"},"timestamp":"2025-06-20T11:02:48.266392Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations?status=active","status_code":200,"duration_ms":2.01,"response_size":1122,"timestamp":"2025-06-20T11:02:48.268333Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["106"],"content-type":["application/json"]},"body":{"name":"Test Organisation","code":"TEST001","details":{"industry":"Technology"},"remarks":"Test remarks"},"timestamp":"2025-06-20T11:02:48.284042Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations","status_code":201,"duration_ms":2.26,"response_size":390,"timestamp":"2025-06-20T11:02:48.286232Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.294660Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations","status_code":422,"duration_ms":1.77,"response_size":252,"timestamp":"2025-06-20T11:02:48.296370Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["47"],"content-type":["application/json"]},"body":{"name":"Test Organisation","code":"DUPLICATE"},"timestamp":"2025-06-20T11:02:48.304987Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations","status_code":422,"duration_ms":1.94,"response_size":186,"timestamp":"2025-06-20T11:02:48.306864Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.315464Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/1","status_code":200,"duration_ms":1.88,"response_size":476,"timestamp":"2025-06-20T11:02:48.317278Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/999","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.325498Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/999","status_code":404,"duration_ms":1.92,"response_size":135,"timestamp":"2025-06-20T11:02:48.327361Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["63"],"content-type":["application/json"]},"body":{"name":"New Name","code":"NEW001","remarks":"Updated remarks"},"timestamp":"2025-06-20T11:02:48.335924Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/organisations/1","status_code":200,"duration_ms":4.17,"response_size":416,"timestamp":"2025-06-20T11:02:48.340037Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/organisations/999","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["19"],"content-type":["application/json"]},"body":{"name":"New Name"},"timestamp":"2025-06-20T11:02:48.348803Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/organisations/999","status_code":404,"duration_ms":1.41,"response_size":135,"timestamp":"2025-06-20T11:02:48.350154Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.359456Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","status_code":200,"duration_ms":1.95,"response_size":443,"timestamp":"2025-06-20T11:02:48.361327Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.371241Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","status_code":422,"duration_ms":1.62,"response_size":186,"timestamp":"2025-06-20T11:02:48.372795Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.383250Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations","status_code":401,"duration_ms":0.7,"response_size":145,"timestamp":"2025-06-20T11:02:48.383868Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.384504Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations","status_code":401,"duration_ms":0.25,"response_size":145,"timestamp":"2025-06-20T11:02:48.384751Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.385279Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/1","status_code":401,"duration_ms":0.23,"response_size":145,"timestamp":"2025-06-20T11:02:48.385506Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.386159Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/organisations/1","status_code":401,"duration_ms":0.36,"response_size":145,"timestamp":"2025-06-20T11:02:48.386479Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.387181Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","status_code":401,"duration_ms":0.27,"response_size":145,"timestamp":"2025-06-20T11:02:48.387438Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.398057Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/1","status_code":200,"duration_ms":2.04,"response_size":495,"timestamp":"2025-06-20T11:02:48.400014Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/2","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.410641Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/2","status_code":403,"duration_ms":1.75,"response_size":134,"timestamp":"2025-06-20T11:02:48.412330Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.422393Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/1","status_code":403,"duration_ms":1.74,"response_size":134,"timestamp":"2025-06-20T11:02:48.424077Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.443347Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.55,"response_size":417,"timestamp":"2025-06-20T11:02:48.444820Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.457602Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.49,"response_size":417,"timestamp":"2025-06-20T11:02:48.459029Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.477940Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.58,"response_size":407,"timestamp":"2025-06-20T11:02:48.479431Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.492279Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.54,"response_size":407,"timestamp":"2025-06-20T11:02:48.493757Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.506587Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":403,"duration_ms":0.82,"response_size":142,"timestamp":"2025-06-20T11:02:48.507327Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.520006Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":403,"duration_ms":0.78,"response_size":142,"timestamp":"2025-06-20T11:02:48.520716Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.533040Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":401,"duration_ms":0.52,"response_size":145,"timestamp":"2025-06-20T11:02:48.533505Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.545859Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":403,"duration_ms":0.75,"response_size":142,"timestamp":"2025-06-20T11:02:48.546550Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.559246Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.45,"response_size":417,"timestamp":"2025-06-20T11:02:48.560637Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.573253Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.48,"response_size":417,"timestamp":"2025-06-20T11:02:48.574675Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.587349Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":403,"duration_ms":0.77,"response_size":142,"timestamp":"2025-06-20T11:02:48.588054Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.600555Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.46,"response_size":407,"timestamp":"2025-06-20T11:02:48.601956Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["59"],"content-type":["application/json"]},"body":{"name":"test-role","organisation_id":1,"guard_name":"api"},"timestamp":"2025-06-20T11:02:48.614749Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/roles","status_code":201,"duration_ms":2.13,"response_size":256,"timestamp":"2025-06-20T11:02:48.616823Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["49"],"content-type":["application/json"]},"body":{"name":"test-system-role","guard_name":"system"},"timestamp":"2025-06-20T11:02:48.629712Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/roles","status_code":201,"duration_ms":1.85,"response_size":269,"timestamp":"2025-06-20T11:02:48.631498Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["65"],"content-type":["application/json"]},"body":{"name":"invalid-role","organisation_id":1,"guard_name":"system"},"timestamp":"2025-06-20T11:02:48.644175Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/roles","status_code":422,"duration_ms":1.63,"response_size":217,"timestamp":"2025-06-20T11:02:48.645745Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles/3","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.658217Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles/3","status_code":200,"duration_ms":1.22,"response_size":254,"timestamp":"2025-06-20T11:02:48.659384Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.671683Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":401,"duration_ms":0.52,"response_size":145,"timestamp":"2025-06-20T11:02:48.672150Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.684432Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":1.42,"response_size":417,"timestamp":"2025-06-20T11:02:48.685797Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.698720Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":403,"duration_ms":0.8,"response_size":142,"timestamp":"2025-06-20T11:02:48.699449Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.727285Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":200,"duration_ms":2.77,"response_size":2204,"timestamp":"2025-06-20T11:02:48.729974Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.743690Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":200,"duration_ms":2.48,"response_size":2192,"timestamp":"2025-06-20T11:02:48.746108Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.759370Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":200,"duration_ms":2.38,"response_size":800,"timestamp":"2025-06-20T11:02:48.761686Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users?organisation_id=1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"organisation_id":"1"},"timestamp":"2025-06-20T11:02:48.774956Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users?organisation_id=1","status_code":200,"duration_ms":2.29,"response_size":787,"timestamp":"2025-06-20T11:02:48.777176Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users?organisation_id=2","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"organisation_id":"2"},"timestamp":"2025-06-20T11:02:48.790560Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users?organisation_id=2","status_code":422,"duration_ms":1.37,"response_size":197,"timestamp":"2025-06-20T11:02:48.791870Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.805197Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":200,"duration_ms":2.32,"response_size":798,"timestamp":"2025-06-20T11:02:48.807461Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.821421Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":403,"duration_ms":0.97,"response_size":134,"timestamp":"2025-06-20T11:02:48.822328Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.835607Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/6","status_code":200,"duration_ms":1.36,"response_size":411,"timestamp":"2025-06-20T11:02:48.836900Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.850339Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/6","status_code":200,"duration_ms":1.54,"response_size":422,"timestamp":"2025-06-20T11:02:48.851824Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/7","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.864958Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/7","status_code":403,"duration_ms":1.18,"response_size":134,"timestamp":"2025-06-20T11:02:48.866088Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/4","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.879131Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/4","status_code":200,"duration_ms":1.61,"response_size":410,"timestamp":"2025-06-20T11:02:48.880685Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:48.893817Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/6","status_code":403,"duration_ms":1.18,"response_size":134,"timestamp":"2025-06-20T11:02:48.894938Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["99"],"content-type":["application/json"]},"body":{"name":"New User","email":"<EMAIL>","password":"***FILTERED***","organisation_ids":[1,2]},"timestamp":"2025-06-20T11:02:48.908205Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":201,"duration_ms":3.3,"response_size":438,"timestamp":"2025-06-20T11:02:48.911444Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["104"],"content-type":["application/json"]},"body":{"name":"New Org User","email":"<EMAIL>","password":"***FILTERED***","organisation_ids":[1]},"timestamp":"2025-06-20T11:02:48.925089Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":201,"duration_ms":3.31,"response_size":375,"timestamp":"2025-06-20T11:02:48.928336Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["101"],"content-type":["application/json"]},"body":{"name":"Invalid User","email":"<EMAIL>","password":"***FILTERED***","organisation_ids":[2]},"timestamp":"2025-06-20T11:02:48.941866Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":422,"duration_ms":2.01,"response_size":215,"timestamp":"2025-06-20T11:02:48.943821Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["85"],"content-type":["application/json"]},"body":{"name":"Default Org User","email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T11:02:48.957248Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":201,"duration_ms":3.24,"response_size":381,"timestamp":"2025-06-20T11:02:48.960421Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["88"],"content-type":["application/json"]},"body":{"name":"Unauthorized User","email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T11:02:48.973921Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":403,"duration_ms":1.01,"response_size":134,"timestamp":"2025-06-20T11:02:48.974875Z"} 
[2025-06-20 11:02:48] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["23"],"content-type":["application/json"]},"body":{"name":"Updated Name"},"timestamp":"2025-06-20T11:02:48.996379Z"} 
[2025-06-20 11:02:48] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/6","status_code":200,"duration_ms":2.1,"response_size":399,"timestamp":"2025-06-20T11:02:48.998412Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["30"],"content-type":["application/json"]},"body":{"name":"Updated Member Name"},"timestamp":"2025-06-20T11:02:49.012324Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/6","status_code":200,"duration_ms":2.27,"response_size":404,"timestamp":"2025-06-20T11:02:49.014509Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/7","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["28"],"content-type":["application/json"]},"body":{"name":"Should Not Update"},"timestamp":"2025-06-20T11:02:49.027943Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/7","status_code":403,"duration_ms":1.42,"response_size":134,"timestamp":"2025-06-20T11:02:49.029300Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["30"],"content-type":["application/json"]},"body":{"name":"Unauthorized Update"},"timestamp":"2025-06-20T11:02:49.042747Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/6","status_code":403,"duration_ms":1.19,"response_size":134,"timestamp":"2025-06-20T11:02:49.043868Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.057868Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/suspend","status_code":200,"duration_ms":1.57,"response_size":404,"timestamp":"2025-06-20T11:02:49.059380Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.073469Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/suspend","status_code":200,"duration_ms":1.9,"response_size":399,"timestamp":"2025-06-20T11:02:49.075310Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/7/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.089180Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/7/suspend","status_code":403,"duration_ms":1.3,"response_size":134,"timestamp":"2025-06-20T11:02:49.090400Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/activate","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.104566Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/activate","status_code":200,"duration_ms":1.5,"response_size":400,"timestamp":"2025-06-20T11:02:49.105992Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/activate","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.119792Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/activate","status_code":200,"duration_ms":1.7,"response_size":405,"timestamp":"2025-06-20T11:02:49.121435Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/7/activate","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.135194Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/7/activate","status_code":403,"duration_ms":1.23,"response_size":134,"timestamp":"2025-06-20T11:02:49.136365Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.150684Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/suspend","status_code":403,"duration_ms":1.31,"response_size":134,"timestamp":"2025-06-20T11:02:49.151922Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/activate","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.165886Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/activate","status_code":403,"duration_ms":1.26,"response_size":134,"timestamp":"2025-06-20T11:02:49.167081Z"} 
[2025-06-20 11:02:49] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T11:02:49.187902Z"} 
[2025-06-20 11:02:49] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":200,"duration_ms":2.89,"response_size":1228,"timestamp":"2025-06-20T11:02:49.190707Z"} 

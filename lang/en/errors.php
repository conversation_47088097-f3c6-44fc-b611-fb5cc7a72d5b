<?php

declare(strict_types=1);

return [
    // Standard error messages for different exception types
    'validation' => 'Validation failed',
    'authentication' => 'Authentication required',
    'authorization' => 'Access denied',
    'permission' => 'Insufficient permissions',
    'not_found' => 'Resource not found',
    'method_not_allowed' => 'Method not allowed',
    'throttle' => 'Too many requests',
    'bad_request' => 'Bad request',
    'server_error' => 'Internal server error',
    'service_unavailable' => 'Service temporarily unavailable',
    'generic' => 'An error occurred while processing your request',
    'bad_gateway' => 'Bad gateway',

    // Business logic errors
    'business_logic_error' => 'Business logic error',
    'resource_state_conflict' => 'Resource state conflict',
    
    // User role related errors
    'role_assignment_failed' => 'Role assignment failed',
    'role_removal_failed' => 'Role removal failed',
    'role_transfer_failed' => 'Role transfer failed',
    
    // Organisation errors
    'organisation_already_suspended' => 'Organisation is already suspended',
    
    // Invitation errors
    'invitation_expired' => 'Invitation has expired',
    'invitation_usage_limit_reached' => 'Invitation usage limit reached',
    'invitation_processing_error' => 'Invitation processing error',
    
    // Email verification errors
    'verification_code_send_error' => 'Failed to send verification code',
];

<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1;

use App\Models\Role;
use App\Services\PermissionService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

final class RoleController extends ApiController
{
    public function __construct(
        private readonly PermissionService $permissionService
    ) {
        // Set up automatic authorization for Role resource
        $this->authorizeResource(Role::class, 'role');
    }

    /**
     * Display a listing of roles for the current user's organisation.
     * For system users (no organisation), return all system-wide roles.
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();

        $userOrganisationIds = $user->getOrganisationIds();

        if ($userOrganisationIds->isNotEmpty()) {
            // User belongs to organisations, get organisation roles
            $roles = Role::whereIn('organisation_id', $userOrganisationIds)->get();
        } else {
            // System user, get system-wide roles
            $roles = Role::whereNull('organisation_id')->get();
        }

        return $this->successResponse($roles, 'api.role.list_retrieved');
    }

    /**
     * Store a newly created role.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'guard_name' => 'sometimes|string|in:web,api,system',
            'organisation_id' => 'nullable|integer|exists:organisations,id',
        ]);

        $user = $request->user();
        $organisationId = $request->input('organisation_id');
        $guardName = $request->input('guard_name', 'api');
        if ($organisationId !== null) {
            // Organisation-specific role creation
            $this->authorize('createForOrganisation', [Role::class, $organisationId]);

            // Validate guard name for organisation roles
            if ($guardName === 'system') {
                return $this->validationErrorResponse(
                    ['guard_name' => [__('errors.business_logic_error')]],
                    'errors.business_logic_error'
                );
            }
        } else {
            // System-wide role creation
            $this->authorize('createSystemRole', Role::class);

            // System roles should use 'system' guard
            if ($guardName !== 'system') {
                $guardName = 'system';
            }
        }

        $role = $this->permissionService->createRole(
            $request->input('name'),
            $guardName,
            $organisationId
        );

        return $this->successResponse($role, 'api.role.created', 201);
    }

    /**
     * Display the specified role.
     */
    public function show(Request $request, Role $role): JsonResponse
    {
        return $this->successResponse($role, 'api.role.retrieved');
    }

    /**
     * Update the specified role.
     * Note: This is primarily for role metadata updates, not for role assignments.
     * Role assignments should use UserRoleController.
     */
    public function update(Request $request, Role $role): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
        ]);

        // Check if role name can be updated using Policy
        $this->authorize('updateName', $role);

        $role->update([
            'name' => $request->input('name'),
        ]);

        return $this->successResponse($role, 'api.role.updated');
    }

    /**
     * Remove the specified role.
     */
    public function destroy(Request $request, Role $role): JsonResponse
    {
        // Check if role can be safely deleted using Policy
        $this->authorize('safeDelete', $role);

        $role->delete();

        return $this->successResponse(null, 'api.role.deleted');
    }
}
